<template>
  <div class="page-wrap">
    <!-- 顶部切换 -->
    <div class="segmented">
      <button :class="['seg-item', { active: activeTab==='major' }]" @click="activeTab='major'">大题题型</button>
      <button :class="['seg-item', { active: activeTab==='nonMajor' }]" @click="activeTab='nonMajor'">非大题</button>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-input
          v-model="keyword"
          placeholder="搜索：名称 / 描述 / 内容"
          clearable
          @keyup.enter="reload"
          class="w-96"
      >
        <template #append><el-button @click="reload">搜索</el-button></template>
      </el-input>

      <div class="grow"></div>

      <!-- 显示列控制 -->
      <el-popover placement="bottom-end" trigger="click" width="220">
        <div class="col-ctrl">
          <div class="col-ctrl-title">显示列</div>
          <el-checkbox v-model="columnCtrl.englishName">英文名称</el-checkbox>
          <el-checkbox v-model="columnCtrl.description">描述</el-checkbox>
        </div>
        <template #reference>
          <el-button plain>显示列</el-button>
        </template>
      </el-popover>

      <el-button type="primary" @click="openCreate">新建提示词</el-button>
    </div>

    <!-- 大题区域 -->
    <div v-if="activeTab==='major'">
      <div class="sub-segmented">
        <button :class="['sub-item', { active: majorMode==='questionType' }]" @click="majorMode='questionType'">按题型筛选</button>
        <button :class="['sub-item', { active: majorMode==='majorStageType' }]" @click="majorMode='majorStageType'">按阶段筛选</button>
      </div>

      <div class="filters">
        <el-form :inline="true">
          <template v-if="majorMode==='questionType'">
            <el-form-item label="题型">
              <el-select
                  v-model="filters.questionType"
                  placeholder="请选择或输入题型"
                  filterable allow-create default-first-option clearable
                  @change="reload" class="w-64"
              >
                <el-option v-for="opt in dicts.questionTypes" :key="opt" :label="opt" :value="opt" />
              </el-select>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="阶段">
              <el-select
                  v-model="filters.majorStageType"
                  placeholder="请选择阶段"
                  filterable allow-create default-first-option clearable
                  @change="reload" class="w-64"
              >
                <el-option v-for="opt in dicts.majorStageTypes" :key="opt" :label="opt" :value="opt" />
              </el-select>
            </el-form-item>
          </template>

          <!-- 角色筛选（仅大题；string + allow-create） -->
          <el-form-item label="角色">
            <el-select
                v-model="filters.role"
                placeholder="全部角色"
                filterable allow-create default-first-option clearable
                class="w-64" @change="reload"
            >
              <el-option v-for="opt in dicts.roles" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>

          <!-- 是否发送图片筛选（全部/是/否） -->
          <el-form-item label="是否发送图片">
            <el-select v-model="filters.hasImage" placeholder="全部" clearable class="w-64" @change="reload">
              <el-option label="全部" :value="''" />
              <el-option label="是" :value="'1'" />
              <el-option label="否" :value="'0'" />
            </el-select>
          </el-form-item>

          <el-form-item><el-button @click="clearMajorFilters">清空筛选</el-button></el-form-item>
        </el-form>
      </div>

      <el-alert type="info" show-icon class="mb-3" :closable="false" title="当前仅显示「大题类型」的提示词" />

      <el-table :data="records" border stripe v-loading="loading" class="mb-3">
        <el-table-column prop="name" label="名称" min-width="160" />

        <!-- 默认隐藏列：英文名称、描述 -->
        <el-table-column v-if="columnCtrl.englishName" prop="englishName" label="英文名称" min-width="160" />
        <el-table-column v-if="columnCtrl.description" prop="description" label="描述" min-width="220" show-overflow-tooltip />

        <el-table-column prop="questionType" label="题型" width="140" />
        <el-table-column prop="majorStageType" label="阶段" width="140" />
        <el-table-column prop="role" label="角色" width="140" />
        <el-table-column prop="hasImage" label="是否发送图片" width="130">
          <template #default="{ row }">
            <el-tag :type="row.hasImage ? 'success' : 'info'">{{ row.hasImage ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="提示词内容" min-width="260">
          <template #default="{ row }">
            <el-popover trigger="hover" :width="600" placement="top">
              <pre class="content-pre">{{ row.promptContent }}</pre>
              <template #reference>
                <div class="content-ellipsis">{{ row.promptContent }}</div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="标签" min-width="180">
          <template #default="{ row }">
            <el-tag v-for="(t,i) in (row.tags || '').split(',').filter(Boolean)" :key="i" class="mr-1 mb-1">{{ t }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="270" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="openEdit(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
            <el-button size="small" type="info" @click="showLog(row)">变更日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
          background layout="prev, pager, next, jumper, sizes, total"
          :page-sizes="[10,20,50,100]" :current-page="page.pageNumber" :page-size="page.pageSize" :total="total"
          @current-change="(p)=>{ page.pageNumber=p; reload(); }"
          @size-change="(s)=>{ page.pageSize=s; reload(); }"
      />
    </div>

    <!-- 非大题区域（不展示角色列、不编辑角色） -->
    <div v-else>
      <div class="filters">
        <el-form :inline="true">
          <el-form-item label="类别">
            <el-select
                v-model="filters.category"
                placeholder="请选择类别"
                filterable clearable
                @change="reload" class="w-64"
            >
              <el-option v-for="opt in dicts.categories" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>

          <!-- 是否发送图片筛选（全部/是/否） -->
          <el-form-item label="是否发送图片">
            <el-select v-model="filters.hasImage" placeholder="全部" clearable class="w-64" @change="reload">
              <el-option label="全部" :value="''" />
              <el-option label="是" :value="'1'" />
              <el-option label="否" :value="'0'" />
            </el-select>
          </el-form-item>

          <el-form-item><el-button @click="clearNonMajorFilters">清空筛选</el-button></el-form-item>
        </el-form>
      </div>

      <el-alert type="info" show-icon class="mb-3" :closable="false" title="当前仅显示「非大题」的提示词" />

      <el-table :data="records" border stripe v-loading="loading" class="mb-3">
        <el-table-column prop="name" label="名称" min-width="160" />

        <!-- 默认隐藏列：英文名称、描述 -->
        <el-table-column v-if="columnCtrl.englishName" prop="englishName" label="英文名称" min-width="160" />
        <el-table-column v-if="columnCtrl.description" prop="description" label="描述" min-width="220" show-overflow-tooltip />

        <el-table-column prop="category" label="类别" width="140" />
        <el-table-column prop="hasImage" label="是否发送图片" width="130">
          <template #default="{ row }">
            <el-tag :type="row.hasImage ? 'success' : 'info'">{{ row.hasImage ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="提示词内容" min-width="260">
          <template #default="{ row }">
            <el-popover trigger="hover" :width="600" placement="top">
              <pre class="content-pre">{{ row.promptContent }}</pre>
              <template #reference>
                <div class="content-ellipsis">{{ row.promptContent }}</div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="标签" min-width="180">
          <template #default="{ row }">
            <el-tag v-for="(t,i) in (row.tags || '').split(',').filter(Boolean)" :key="i" class="mr-1 mb-1">{{ t }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="270" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="openEdit(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
            <el-button size="small" type="info" @click="showLog(row)">变更日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
          background layout="prev, pager, next, jumper, sizes, total"
          :page-sizes="[10,20,50,100]" :current-page="page.pageNumber" :page-size="page.pageSize" :total="total"
          @current-change="(p)=>{ page.pageNumber=p; reload(); }"
          @size-change="(s)=>{ page.pageSize=s; reload(); }"
      />
    </div>

    <!-- 编辑/新增弹窗 -->
    <el-dialog v-model="editVisible" :title="editForm.id ? '编辑提示词' : '新建提示词'" width="760px">
      <el-form :model="editForm" :rules="rules" ref="editRef" label-width="120px">
        <el-form-item label="名称" prop="name"><el-input v-model="editForm.name" placeholder="名称" /></el-form-item>
        <el-form-item label="英文名称" v-if="false"><!-- 保留占位，真实列由“显示列”控制 --></el-form-item>
        <el-form-item label="描述"><el-input v-model="editForm.description" placeholder="描述" /></el-form-item>

        <template v-if="activeTab==='major'">
          <el-form-item label="是否大题"><el-switch v-model="editForm.isMajorType" :active-value="true" :inactive-value="false" disabled /></el-form-item>
          <el-form-item label="类别"><el-input v-model="editForm.category" disabled /></el-form-item>
          <el-form-item label="题型">
            <el-select v-model="editForm.questionType" placeholder="请选择或输入题型" filterable allow-create default-first-option clearable class="w-64">
              <el-option v-for="opt in dicts.questionTypes" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>
          <el-form-item label="阶段">
            <el-select v-model="editForm.majorStageType" filterable allow-create default-first-option clearable class="w-64" placeholder="请选择阶段">
              <el-option v-for="opt in dicts.majorStageTypes" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>

          <!-- 仅大题允许编辑角色（string + allow-create） -->
          <el-form-item label="角色">
            <el-select v-model="editForm.role" placeholder="请选择或输入角色" filterable allow-create default-first-option clearable class="w-64">
              <el-option v-for="opt in dicts.roles" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>

          <el-form-item label="是否发送图片">
            <el-switch v-model="editForm.hasImage" :active-value="true" :inactive-value="false" />
          </el-form-item>
        </template>

        <!-- 非大题不显示角色字段，但可设置“是否发送图片” -->
        <template v-else>
          <el-form-item label="是否大题"><el-switch v-model="editForm.isMajorType" :active-value="false" :inactive-value="true" disabled /></el-form-item>
          <el-form-item label="类别">
            <el-select v-model="editForm.category" filterable clearable class="w-64" placeholder="请选择类别">
              <el-option v-for="opt in dicts.categories" :key="opt" :label="opt" :value="opt" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否发送图片">
            <el-switch v-model="editForm.hasImage" :active-value="true" :inactive-value="false" />
          </el-form-item>
        </template>

        <el-form-item label="标签（逗号分隔）"><el-input v-model="editForm.tags" placeholder="如：NLP,语文,示例" /></el-form-item>

        <el-form-item label="提示词内容" prop="promptContent">
          <el-input v-model="editForm.promptContent" type="textarea" :autosize="{minRows:6,maxRows:18}" placeholder="请输入提示词内容" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editVisible=false">取消</el-button>
        <el-button type="primary" @click="submitEdit">保存</el-button>
      </template>
    </el-dialog>

    <!-- 变更日志 -->
    <el-dialog title="Prompt 变更日志" v-model="logDialogVisible" width="60%">
      <el-table :data="changeLogs" border>
        <el-table-column prop="createTime" label="变更时间" width="180" />
        <el-table-column prop="oldValue" label="修改前内容(点击复制)">
          <template #default="{ row }">
            <span class="copy-cell" @click="copyText(row.oldValue)">
              {{ row.oldValue && row.oldValue.length>60 ? row.oldValue.slice(0,30) + ' ………… ' + row.oldValue.slice(-30) : row.oldValue }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="newValue" label="修改后内容(点击复制)">
          <template #default="{ row }">
            <span class="copy-cell" @click="copyText(row.newValue)">
              {{ row.newValue && row.newValue.length>60 ? row.newValue.slice(0,30) + ' ………… ' + row.newValue.slice(-30) : row.newValue }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer><el-button @click="logDialogVisible=false">关闭</el-button></template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus';

const COL_STORAGE_KEY = 'prompt_column_ctrl';

export default {
  name: 'PromptsManager',
  data() {
    return {
      activeTab: 'major',
      majorMode: 'questionType',
      keyword: '',
      // role 仅用于大题筛选；hasImage 三态：'' | '1' | '0'
      filters: { questionType: '', majorStageType: '', category: '', role: '', hasImage: '' },
      dicts: { questionTypes: [], majorStageTypes: [], categories: [], roles: [] },
      page: { pageNumber: 1, pageSize: 10, searchCount: true },
      total: 0,
      records: [],
      loading: false,
      // 列显示控制：英文名称/描述默认关闭
      columnCtrl: { englishName: false, description: false },

      editVisible: false,
      editForm: {
        id: null,
        englishName: '', description: '', name: '',
        isMajorType: true, questionType: '', category: '大题类型',
        promptContent: '', majorStageType: '', tags: '',
        role: '', hasImage: false
      },
      rules: {
        name: [{ required: true, message: '请填写名称', trigger: 'blur' }],
        promptContent: [{ required: true, message: '请填写提示词内容', trigger: 'blur' }]
      },
      logDialogVisible: false,
      changeLogs: [],
      currentLogKey: ''
    };
  },
  mounted() {
    // 载入列显示偏好
    try {
      const saved = JSON.parse(localStorage.getItem(COL_STORAGE_KEY) || '{}');
      this.columnCtrl = { englishName: !!saved.englishName, description: !!saved.description };
    } catch (_) {}
    this.loadDictionaries().then(() => this.reload());
  },
  watch: {
    columnCtrl: {
      deep: true,
      handler(val) {
        localStorage.setItem(COL_STORAGE_KEY, JSON.stringify(val || {}));
      }
    },
    activeTab() {
      this.page.pageNumber = 1; this.keyword = '';
      this.filters = { questionType: '', majorStageType: '', category: '', role: '', hasImage: '' };
      this.reload();
    },
    majorMode() {
      this.filters.questionType = ''; this.filters.majorStageType = '';
      this.page.pageNumber = 1; this.reload();
    }
  },
  methods: {
    async loadDictionaries() {
      try {
        // 大题：抽取题型/阶段/角色
        const dtoMajor = { category: '大题类型', isMajorType: true, page: { pageNumber: 1, pageSize: 1000, searchCount: false } };
        const majorRes = await this.$axios.post('/api/prompts/page', dtoMajor);
        const majorList = (majorRes?.data?.records || []);
        this.dicts.questionTypes = Array.from(new Set(majorList.map(x => x.questionType).filter(Boolean)));
        this.dicts.majorStageTypes = Array.from(new Set(majorList.map(x => x.majorStageType).filter(Boolean)));
        this.dicts.roles = Array.from(new Set(majorList.map(x => x.role).filter(Boolean)));

        // 非大题：抽取类别
        const dtoNonMajor = { isMajorType: false, page: { pageNumber: 1, pageSize: 1000, searchCount: false } };
        const nonMajorRes = await this.$axios.post('/api/prompts/page', dtoNonMajor);
        const nonMajorList = (nonMajorRes?.data?.records || []);
        this.dicts.categories = Array.from(new Set(nonMajorList.map(x => x.category).filter(Boolean)));
      } catch (e) {}
    },
    buildQueryDTO() {
      const dto = { page: { ...this.page }, keyword: this.keyword || undefined };
      // 解析 hasImage 三态到布尔/空
      const parseHasImage = (v) => v === '1' ? true : v === '0' ? false : undefined;

      if (this.activeTab === 'major') {
        dto.isMajorType = true; dto.category = '大题类型';
        if (this.majorMode === 'questionType' && this.filters.questionType) dto.questionType = this.filters.questionType;
        if (this.majorMode === 'majorStageType' && this.filters.majorStageType) dto.majorStageType = this.filters.majorStageType;
        if (this.filters.role) dto.role = this.filters.role;
        const hi = parseHasImage(this.filters.hasImage);
        if (typeof hi === 'boolean') dto.hasImage = hi;
      } else {
        dto.isMajorType = false;
        if (this.filters.category) dto.category = this.filters.category;
        const hi = parseHasImage(this.filters.hasImage);
        if (typeof hi === 'boolean') dto.hasImage = hi;
      }
      return dto;
    },
    async reload() {
      this.loading = true;
      try {
        const res = await this.$axios.post('/api/prompts/page', this.buildQueryDTO());
        const pageData = res?.data || {};
        this.records = pageData.records || []; this.total = pageData.total || 0;
      } catch (e) { ElMessage.error('加载失败'); } finally { this.loading = false; }
    },
    clearMajorFilters() {
      this.filters.questionType = '';
      this.filters.majorStageType = '';
      this.filters.role = '';
      this.filters.hasImage = '';
      this.page.pageNumber = 1; this.reload();
    },
    clearNonMajorFilters() {
      this.filters.category = '';
      this.filters.hasImage = '';
      this.page.pageNumber = 1; this.reload();
    },
    openCreate() {
      if (this.activeTab === 'major') {
        this.editForm = {
          id: null, englishName: '', description: '', name: '',
          isMajorType: true,
          questionType: this.filters.questionType || '',
          category: '大题类型',
          promptContent: '',
          majorStageType: this.filters.majorStageType || '',
          tags: '',
          role: this.filters.role || '',
          hasImage: this.filters.hasImage === '1' ? true : false
        };
      } else {
        this.editForm = {
          id: null, englishName: '', description: '', name: '',
          isMajorType: false, questionType: '', category: this.filters.category || '',
          promptContent: '', majorStageType: '', tags: '',
          hasImage: this.filters.hasImage === '1' ? true : false
        };
      }
      this.editVisible = true;
    },
    openEdit(row) {
      this.editForm = { ...row };
      if (typeof this.editForm.isMajorType !== 'boolean') this.editForm.isMajorType = String(this.editForm.isMajorType) === '1';
      if (typeof this.editForm.hasImage !== 'boolean') this.editForm.hasImage = String(this.editForm.hasImage) === '1' || this.editForm.hasImage === 1;

      if (this.activeTab === 'major') {
        this.editForm.isMajorType = true; this.editForm.category = '大题类型';
        if (!this.editForm.role) this.editForm.role = '';
      } else {
        this.editForm.isMajorType = false;
        // 非大题：不展示/不提交 role
      }
      this.editVisible = true;
    },
    submitEdit() {
      this.$refs.editRef.validate(async (ok) => {
        if (!ok) return;
        try {
          const payload = { ...this.editForm, isMajorType: this.editForm.isMajorType ? 1 : 0 };
          if (this.activeTab !== 'major') delete payload.role;

          if (payload.id) { await this.$axios.post('/api/prompts/update', payload); ElMessage.success('更新成功'); }
          else { await this.$axios.post('/api/prompts/add', payload); ElMessage.success('新增成功'); }

          this.editVisible = false; this.reload(); this.loadDictionaries();
        } catch (e) { ElMessage.error('保存失败'); }
      });
    },
    async handleDelete(row) {
      try {
        await this.$confirm(`确定删除「${row.name || row.englishName || row.id}」？`, '删除确认', { type: 'warning', confirmButtonText: '删除', cancelButtonText: '取消' });
        await this.$axios.post('/api/prompts/delete', {}, { params: { id: row.id } });
        this.reload(); this.loadDictionaries(); ElMessage.success('删除成功');
      } catch (e) {}
    },
    async showLog(row) {
      // 优先使用 id 作为日志 key
      this.currentLogKey = (row.id !== undefined && row.id !== null) ? String(row.id) : (row.englishName || row.name);
      this.logDialogVisible = true;
      const dto = { promptKey: this.currentLogKey, page: { pageNumber: 1, pageSize: 200, searchCount: false } };
      try { const res = await this.$axios.post('/api/promptChangeLog/page', dto); this.changeLogs = res?.data?.records || []; }
      catch (e) { ElMessage.error('获取变更日志失败'); }
    },
    copyText(text) {
      if (!text) return;
      navigator.clipboard.writeText(text).then(() => ElMessage.success('已复制到剪贴板')).catch(() => ElMessage.error('复制失败'));
    }
  }
};
</script>

<style scoped>
.page-wrap { display: flex; flex-direction: column; gap: 12px; }
.mb-3 { margin-bottom: 12px; }
.w-64 { width: 256px; }
.w-96 { width: 384px; }
.grow { flex: 1; }

/* 顶部胶囊分段 */
.segmented {
  display: inline-flex;
  align-items: center;
  background: #f5f7fa;
  border-radius: 999px;
  padding: 6px;
  gap: 6px;
  width: max-content;
  box-shadow: inset 0 0 0 1px #e4e7ed;
}
.seg-item {
  appearance: none;
  border: none;
  border-radius: 999px;
  padding: 8px 18px;
  font-size: 14px;
  line-height: 1;
  background: transparent;
  color: #606266;
  cursor: pointer;
  transition: all .18s ease;
}
.seg-item:hover { background: #eef2f7; color: #303133; }
.seg-item.active { background: #409eff; color: #fff; box-shadow: 0 4px 10px rgba(64,158,255,.35); }

/* 次级分段 */
.sub-segmented {
  display: inline-flex;
  align-items: center;
  background: #fff;
  border: 1px solid var(--el-border-color);
  border-radius: 10px;
  padding: 4px;
  gap: 4px;
  margin: 10px 0;
}
.sub-item {
  appearance: none;
  border: none;
  border-radius: 8px;
  padding: 6px 12px;
  background: transparent;
  color: #606266;
  cursor: pointer;
  transition: all .18s ease;
}
.sub-item.active { background: #ecf5ff; color: #409eff; }

/* 工具栏与过滤框 */
.toolbar { display: flex; align-items: center; gap: 12px; }
.filters { padding: 10px 12px; background: #fff; border: 1px solid var(--el-border-color); border-radius: 8px; }

/* 列控制 */
.col-ctrl { display: flex; flex-direction: column; gap: 8px; }
.col-ctrl-title { font-weight: 600; color: #303133; margin-bottom: 6px; }

/* 内容预览/省略 */
.content-ellipsis {
  max-width: 520px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #606266;
}
.content-pre {
  max-height: 360px; overflow: auto; white-space: pre-wrap;
  font-family: ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;
  line-height: 1.5; font-size: 13px;
}
.copy-cell { cursor: pointer; color: var(--el-color-primary); white-space: pre-wrap; word-break: break-word; }
</style>
