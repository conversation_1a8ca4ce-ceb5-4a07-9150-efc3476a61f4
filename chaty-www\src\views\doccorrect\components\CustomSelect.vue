<template>
  <div class="custom-select" :class="{ 'is-disabled': disabled }">
    <div
      class="custom-select__trigger"
      :class="{ 'is-active': isOpen }"
      @click="toggleDropdown($event)"
    >
      <div class="custom-select__content">
        <el-icon v-if="selectedOption?.icon" class="custom-select__icon">
          <component :is="selectedOption.icon" />
        </el-icon>
        <span class="custom-select__text">
          {{ selectedOption?.label || placeholder }}
        </span>
      </div>
      <el-icon class="custom-select__arrow" :class="{ 'is-reverse': isOpen }">
        <ArrowDown />
      </el-icon>
    </div>
    
    <transition name="dropdown">
      <div v-if="isOpen" class="custom-select__dropdown">
        <div
          v-for="option in options"
          :key="option.value"
          class="custom-select__option"
          :class="{ 'is-selected': option.value === modelValue }"
          @click="selectOption(option, $event)"
        >
          <div class="custom-select__option-content">
            <el-icon v-if="option.icon" class="custom-select__option-icon">
              <component :is="option.icon" />
            </el-icon>
            <span class="custom-select__option-text">{{ option.label }}</span>
          </div>
          <el-icon v-if="option.value === modelValue" class="custom-select__check">
            <Check />
          </el-icon>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ArrowDown, Check, Document, Grid, Edit, Tools, Trophy, Star, Medal, Flag } from '@element-plus/icons-vue'

export default {
  name: 'CustomSelect',
  components: {
    ArrowDown,
    Check,
    Document,
    Grid,
    Edit,
    Tools,
    Trophy,
    Star,
    Medal,
    Flag
  },
  props: {
    modelValue: {
      type: [String, Number],
      default: null
    },
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change', 'click'],
  data() {
    return {
      isOpen: false
    }
  },
  computed: {
    selectedOption() {
      return this.options.find(option => option.value === this.modelValue)
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside)
  },
  watch: {
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    toggleDropdown(event) {
      // 阻止事件冒泡，防止触发父组件的点击事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      this.$emit('click'); // 发出click事件给父组件
      if (!this.disabled) {
        this.isOpen = !this.isOpen
      }
    },
    selectOption(option, event) {
      // 阻止事件冒泡
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      this.$emit('update:modelValue', option.value)
      this.$emit('change', option.value)
      this.isOpen = false
    },
    handleClickOutside(event) {
      if (this.$el && !this.$el.contains(event.target)) {
        this.isOpen = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-select {
  position: relative;
  display: inline-block;
  
  &.is-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    .custom-select__trigger {
      cursor: not-allowed;
    }
  }
}

.custom-select__trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 27px;
  height: 27px;
  
  &:hover {
    border-color: #c0c4cc;
  }
  
  &.is-active {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    border-left: 4px solid #409eff;
    padding-left: 8px; /* 减少左内边距以补偿边框宽度 */
  }
}

.custom-select__content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.custom-select__icon {
  color: #606266;
  font-size: 16px;
  flex-shrink: 0;
}

.custom-select__text {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
}

.custom-select__arrow {
  color: #c0c4cc;
  font-size: 14px;
  transition: transform 0.3s;
  
  &.is-reverse {
    transform: rotate(180deg);
  }
}

.custom-select__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  margin-top: 4px;
  max-height: 280px;
  overflow-y: auto;
  padding: 8px 0;
  min-width: 100%;
}

.custom-select__option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  min-height: 44px;
  
  &:hover {
    background: #f5f7fa;
  }
  
  &.is-selected {
    background: #ecf5ff;
    color: #409eff;
    border-left: 4px solid #409eff;
    padding-left: 12px; /* 减少左内边距以补偿边框宽度 */

    .custom-select__option-icon {
      color: #409eff;
    }
  }
}

.custom-select__option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.custom-select__option-icon {
  color: #606266;
  font-size: 18px;
  flex-shrink: 0;
}

.custom-select__option-text {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.custom-select__check {
  color: #409eff;
  font-size: 16px;
  flex-shrink: 0;
}

// 下拉动画
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
  transform-origin: top;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0.8) translateY(-10px);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: scaleY(1) translateY(0);
}
</style>
