<template>
  <el-dialog
      v-model="isShow"
      :title="title"
      width="400px"
      :before-close="onClose"
  >
    <el-form
        :model="formData"
        label-position="top"
        ref="scoreSelectionForm"
    >
      <el-form-item label="选择分数类型" prop="selectedScoreType">
        <custom-select
            v-model="formData.selectedScoreType"
            placeholder="请选择分数类型"
            style="width: 100%;"
            :options="scoreTypeOptions"
        />
      </el-form-item>

      <el-form-item label="是否同步所有区域" prop="selectedScoreType">
        <el-switch v-model="formData.isAsyncAllArea" active-text="同步" inactive-text="不同步"></el-switch>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button
          type="primary"
          @click="onSubmit"
          :loading="submitting"
      >
        确认
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import CustomSelect from './CustomSelect.vue'

export default {
  name: "ScoreTypeDialog",
  components: {
    CustomSelect
  },
  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: "请选择分数类型",
      isShow: false,
      formData: {
        selectedScoreType: null,
        isAsyncAllArea: false
      },
      scoreTypeList: [],
      areaIdx: null
    };
  },
  computed: {
    scoreTypeOptions() {
      // 5种循环使用的图标
      const scoreTypeIcons = ['Trophy', 'Star', 'Medal', 'Tools', 'Flag'];

      return this.scoreTypeList.map((type, index) => ({
        value: type,
        label: type,
        icon: scoreTypeIcons[index % scoreTypeIcons.length]
      }));
    }
  },
  methods: {
    /**
     * 打开对话框，并传入可选的分数类型列表
     * @param areaIdx
     * @param {Array<{id: any, name: string}>} types
     * @param selectedScoreType
     */
    show(areaIdx, types, selectedScoreType) {
      this.formData.selectedScoreType = selectedScoreType;
      this.areaIdx = areaIdx;
      this.scoreTypeList = types;
      this.isShow = true;
    },
    /**
     * 关闭对话框并重置表单
     */
    onClose() {
      if (this.$refs.scoreSelectionForm) {
        this.$refs.scoreSelectionForm.resetFields();
      }
      this.areaIdx = null;
      this.isShow = false;
      this.formData.isAsyncAllArea = false;
    },
    /**
     * 点击“确认”后触发，向父组件抛出所选分数类型
     */
    onSubmit() {
      this.$emit("submit", this.areaIdx, this.formData.selectedScoreType, this.formData.isAsyncAllArea);
      this.onClose();
    },
  },
};
</script>

<style scoped>
/* 根据需要自行定制样式 */
</style>
