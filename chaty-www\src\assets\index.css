.el-menu-item {
    font-weight: 700;
    font-size: 18px;
}

.el-button>span {
    font-weight: 700;
}

.el-table thead .el-table__cell .cell {
    font-weight: 700;
}

/* 导航栏图标颜色覆盖 - 使用更强的选择器 */
.menu-bar-grid .menu-area-grid .el-menu-item.is-active,
.menu-bar-grid .menu-area-grid .el-menu-item.is-active .el-icon,
.menu-bar-grid .menu-area-grid .el-menu-item.is-active .el-icon svg,
.menu-bar-grid .menu-area-grid .el-menu-item.is-active .icons {
    background-color: #90EE90 !important;
    color: #00ff00 !important;
    fill: #00ff00 !important;
}

.menu-area-grid .el-menu-item .el-icon,
.menu-area-grid .el-menu-item .el-icon svg {
    color: #ffffff !important;
    fill: #ffffff !important;
}

.menu-area-grid .el-menu-item:hover .el-icon,
.menu-area-grid .el-menu-item:hover .el-icon svg {
    color: #333333 !important;
    fill: #333333 !important;
}